every_possible_country = {

	generate_character = {
		token_base = aigen_financial_expert # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { financial_expert }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_1
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.49
						neutrality > 0.49
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_financial_expert }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_economist # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { economist }
			idea_token = AI_Navy_Generate_Pol_2
			
			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_economist }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_editor # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { editor }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_3
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.49
						neutrality > 0.49
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_editor }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_prince_of_terror # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { prince_of_terror }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_4
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						communism > 0.49
						fascism > 0.49
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_prince_of_terror }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_silent_workhorse # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { silent_workhorse }
			idea_token = AI_Navy_Generate_Pol_5
			
			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_silent_workhorse }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_technocrat # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { technocrat }
			idea_token = AI_Navy_Generate_Pol_6
			
			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_technocrat }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_propaganda_expert # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { propaganda_expert }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_7
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						communism > 0.49
						fascism > 0.49
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_propaganda_expert }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_popular_figurehead # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { popular_figurehead }
			idea_token = AI_Navy_Generate_Pol_8
			
			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_popular_figurehead }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_shadow_of_calles # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { shadow_of_calles }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_9
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						neutrality > 0.29
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_shadow_of_calles }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_social_reformer # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { social_reformer }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_10
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.29
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_social_reformer }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_syncretic_revanchist # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { syncretic_revanchist }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_11
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						fascism > 0.29
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_syncretic_revanchist }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_ambitious_union_boss # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { ambitious_union_boss }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_12

			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						communism > 0.29
					}
				}
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_ambitious_union_boss }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_captain_of_industry # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { captain_of_industry }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_13

			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_captain_of_industry }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_war_industrialist # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { war_industrialist }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_14

			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_war_industrialist }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_armaments_organizer # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { armaments_organizer }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_15

			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_armaments_organizer }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_quartermaster_general # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { quartermaster_general }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_16

			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_quartermaster_general }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_ideological_crusader # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { ideological_crusader }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_17

			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						communism > 0.49
						fascism > 0.49
					}
				}
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_ideological_crusader }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_smooth_talking_charmer # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { smooth_talking_charmer }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_18

			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.29
					}
				}
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_smooth_talking_charmer }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_backroom_backstabber # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { backroom_backstabber }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_19

			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_backroom_backstabber }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_fortification_engineer # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { fortification_engineer }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_20

			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_fortification_engineer }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_infantry_expert # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { infantry_expert }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_21

			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_infantry_expert }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_democratic_reformer # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { democratic_reformer }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_22

			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.29
						neutrality > 0.29
					}
				}
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_democratic_reformer }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_fascist_demagogue # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { fascist_demagogue }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_23

			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						fascism > 0.29
					}
				}
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_fascist_demagogue }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_communist_revolutionary # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { communist_revolutionary }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_24

			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						communism > 0.29
					}
				}
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_communist_revolutionary }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_compassionate_gentleman # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { compassionate_gentleman }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_25

			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.29
						neutrality > 0.29
					}
				}
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_compassionate_gentleman }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_flamboyant_tough_guy # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { flamboyant_tough_guy }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_26

			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						fascism > 0.29
						communism > 0.29
					}
				}
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_flamboyant_tough_guy }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_man_of_the_people # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { man_of_the_people }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_27

			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = {
			army = { small = GFX_idea_aigen_man_of_the_people }
		}
	}
}