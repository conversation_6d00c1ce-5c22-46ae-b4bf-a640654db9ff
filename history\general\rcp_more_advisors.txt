every_possible_country = {

	generate_character = {
		token_base = aigen_financial_expert # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { financial_expert }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_1
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.49
						neutrality > 0.49
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_financial_expert }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_economist # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { economist }
			idea_token = AI_Navy_Generate_Pol_2
			
			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_economist }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_editor # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { editor }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_3
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.49
						neutrality > 0.49
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_editor }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_prince_of_terror # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { prince_of_terror }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_4
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						communism > 0.49
						fascism > 0.49
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_prince_of_terror }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_silent_workhorse # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { silent_workhorse }
			idea_token = AI_Navy_Generate_Pol_5
			
			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_silent_workhorse }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_technocrat # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { technocrat }
			idea_token = AI_Navy_Generate_Pol_6
			
			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_technocrat }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_propaganda_expert # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { propaganda_expert }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_7
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						communism > 0.49
						fascism > 0.49
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_propaganda_expert }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_popular_figurehead # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { popular_figurehead }
			idea_token = AI_Navy_Generate_Pol_8
			
			visible = {
				always = yes
			}
			available = {
				always = yes
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_popular_figurehead }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_shadow_of_calles # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { shadow_of_calles }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_9
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						neutrality > 0.29
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_shadow_of_calles }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_social_reformer # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { social_reformer }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_10
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						democratic > 0.29
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_social_reformer }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_syncretic_revanchist # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { syncretic_revanchist }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_11
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						fascism > 0.29
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_syncretic_revanchist }
		}
	}
}

every_possible_country = {

	generate_character = {
		token_base = aigen_ambitious_union_boss # token will be TAG_token_base
		advisor = {
			slot = political_advisor
			traits = { ambitious_union_boss }
			cost = 100
			idea_token = AI_Navy_Generate_Pol_12
			
			visible = {
				always = yes
			}
			available = {
				ROOT = {
					OR = {
						communism > 0.29
					}
				}
			}
		}
		portraits = { 
			army = { small = GFX_idea_aigen_ambitious_union_boss }
		}
	}
}